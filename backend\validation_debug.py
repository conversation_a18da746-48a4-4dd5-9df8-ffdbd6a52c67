#!/usr/bin/env python3
"""
Comprehensive debug script to analyze transaction extraction issues
"""

import sys
import os
import logging
import pandas as pd
import json

# Disable excessive debug logging from PDF libraries
logging.getLogger('pdfminer').setLevel(logging.WARNING)
logging.getLogger('pdfplumber').setLevel(logging.WARNING)
logging.getLogger('tabula').setLevel(logging.WARNING)

# Configure our logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_comprehensive_extraction(pdf_path: str):
    """
    Comprehensive debug of the entire extraction pipeline
    """
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False

    try:
        from pdf_processor import PDFProcessor

        logger.info(f"🔍 COMPREHENSIVE EXTRACTION DEBUG")
        logger.info(f"PDF: {pdf_path}")
        logger.info("=" * 80)

        # Create processor
        processor = PDFProcessor()

        # Hook into various stages of processing
        original_process_dataframe = processor._process_dataframe_to_transactions
        original_generic_fallback = processor._extract_hdfc_generic_fallback

        extracted_tables = []
        processed_tables = []
        all_raw_transactions = []

        def debug_process_dataframe(df):
            result = original_process_dataframe(df)
            processed_tables.append({
                'input_shape': df.shape,
                'transactions_extracted': len(result),
                'sample_transactions': result[:3] if result else []
            })
            all_raw_transactions.extend(result)
            logger.info(f"🔄 PROCESSED DATAFRAME: Shape {df.shape} -> {len(result)} transactions")
            if result:
                logger.info(f"  Sample transaction: {result[0]}")
            logger.info(f"  DataFrame columns: {list(df.columns)}")
            logger.info(f"  Sample DataFrame data:\n{df.head(3).to_string()}")
            logger.info("-" * 40)
            return result

        def debug_generic_fallback(file_path):
            logger.info(f"🚨 ENTERING GENERIC FALLBACK EXTRACTION")
            result = original_generic_fallback(file_path)
            logger.info(f"🚨 GENERIC FALLBACK RESULT: {len(result)} transactions")
            return result

        # Replace methods
        processor._process_dataframe_to_transactions = debug_process_dataframe
        processor._extract_hdfc_generic_fallback = debug_generic_fallback

        # Process the PDF
        logger.info("🚀 STARTING PDF PROCESSING...")
        result = processor.process_pdf(pdf_path)
        final_transactions = result.get('transactions', [])

        logger.info(f"\n📊 COMPREHENSIVE RESULTS:")
        logger.info(f"Tables extracted: {len(extracted_tables)}")
        logger.info(f"Tables processed: {len(processed_tables)}")
        logger.info(f"Raw transactions: {len(all_raw_transactions)}")
        logger.info(f"Final transactions: {len(final_transactions)}")

        # Analyze final transactions
        logger.info(f"\n🔍 FINAL TRANSACTION ANALYSIS:")
        if final_transactions:
            sample_txn = final_transactions[0]
            logger.info(f"Sample final transaction structure: {list(sample_txn.keys())}")
            logger.info(f"Sample values: {sample_txn}")

            # Check for missing data patterns
            missing_narration = sum(1 for txn in final_transactions if not txn.get('narration', '').strip())
            missing_amounts = sum(1 for txn in final_transactions if not txn.get('withdrawal_amt') and not txn.get('deposit_amt'))

            logger.info(f"Transactions with missing narration: {missing_narration}")
            logger.info(f"Transactions with missing amounts: {missing_amounts}")

            # Show first 10 final transactions
            logger.info(f"\n📋 FIRST 10 FINAL TRANSACTIONS:")
            for i, txn in enumerate(final_transactions[:10]):
                logger.info(f"  {i+1}: Date='{txn.get('date', '')}', Narration='{txn.get('narration', '')}', Withdrawal='{txn.get('withdrawal_amt', '')}', Deposit='{txn.get('deposit_amt', '')}', Balance='{txn.get('closing_balance', '')}'")

        # Compare raw vs final
        logger.info(f"\n⚖️ RAW VS FINAL COMPARISON:")
        logger.info(f"Raw transactions extracted: {len(all_raw_transactions)}")
        logger.info(f"Final transactions after processing: {len(final_transactions)}")
        logger.info(f"Loss during processing: {len(all_raw_transactions) - len(final_transactions)} transactions ({((len(all_raw_transactions) - len(final_transactions)) / len(all_raw_transactions) * 100):.1f}%)")

        return True

    except Exception as e:
        logger.error(f"Error in comprehensive debug: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python validation_debug.py <pdf_path>")
        sys.exit(1)

    pdf_path = sys.argv[1]
    success = debug_comprehensive_extraction(pdf_path)
    sys.exit(0 if success else 1)
