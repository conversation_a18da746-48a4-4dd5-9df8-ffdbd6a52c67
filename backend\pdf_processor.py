import pandas as pd
import tabula
import re
import logging
from typing import List, Dict, Any, Optional, Tuple
import json
from datetime import datetime
import os
import io
import traceback
from hdfc_processor import HDFCBankProcessor

# Optional imports with fallbacks
try:
    import camelot
    CAMELOT_AVAILABLE = True
except ImportError:
    CAMELOT_AVAILABLE = False
    logging.warning("Camelot not available - will use Tabula only for table extraction")

try:
    import pytesseract
    from PIL import Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logging.warning("OCR libraries not available - scanned PDF processing disabled")

try:
    # PyMuPDF is imported as 'fitz' - this is the correct import name
    import fitz  # type: ignore # PyMuPDF package
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    logging.warning("PyMuPDF not available - will use alternative PDF processing")

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    logging.warning("OpenCV not available - image preprocessing disabled")

# Fallback PDF libraries
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

# PyPDF2 is available as fallback but not currently used
PYPDF2_AVAILABLE = False

logger = logging.getLogger(__name__)

class PDFProcessor:
    """
    Advanced PDF processor that handles both regular and scanned PDFs
    Uses Tabula, Camelot, and OCR for comprehensive table extraction
    """
    
    def __init__(self):
        self.supported_date_formats = [
            '%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y',
            '%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d',
            '%d/%m/%y', '%d-%m-%y', '%d.%m.%y',
            '%d %b %Y', '%d %B %Y', '%b %d, %Y'
        ]

        # Initialize HDFC processor
        self.hdfc_processor = HDFCBankProcessor()
        
    def process_pdf(self, file_path: str) -> Dict[str, Any]:
        """
        Main method to process PDF and extract transaction data with HDFC auto-detection
        """
        try:
            logger.info(f"Processing PDF: {file_path}")

            # First, detect if this is an HDFC Bank statement
            text_content = self._extract_text_for_detection(file_path)
            is_hdfc, confidence = self.hdfc_processor.detect_hdfc_bank(text_content)

            logger.info(f"HDFC Bank detection: {is_hdfc} (confidence: {confidence:.2f})")

            if not is_hdfc:
                raise Exception("Only HDFC Bank statements are supported at this time. Please upload a valid HDFC Bank statement.")

            # Extract HDFC metadata
            hdfc_metadata = self.hdfc_processor.extract_hdfc_metadata(text_content)
            logger.info(f"HDFC metadata extracted: {hdfc_metadata}")

            # Determine if PDF is scanned or regular
            is_scanned = self._is_scanned_pdf(file_path)
            logger.info(f"PDF scanned detection result: {is_scanned}")

            if is_scanned:
                logger.info("Detected scanned HDFC PDF, using OCR approach")
                result = self._process_scanned_pdf(file_path)
            else:
                logger.info("Detected regular HDFC PDF, using table extraction")
                result = self._process_hdfc_pdf(file_path)

            # Add HDFC metadata to result
            result['bank_metadata'] = hdfc_metadata
            result['bank_name'] = 'HDFC'
            result['detection_confidence'] = confidence

            return result

        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise Exception(f"Failed to process PDF: {str(e)}")
    
    def _is_scanned_pdf(self, file_path: str) -> bool:
        """
        Determine if PDF is scanned (image-based) or regular (text-based)
        """
        if not PYMUPDF_AVAILABLE:
            # If PyMuPDF is not available, assume it's a regular PDF
            logger.warning("PyMuPDF not available, assuming regular PDF")
            return False

        try:
            doc = fitz.open(file_path)
            text_content = ""

            # Check first few pages for text content
            pages_to_check = min(3, doc.page_count)

            for page_num in range(pages_to_check):
                page = doc[page_num]
                text_content += page.get_text()

            doc.close()

            # If very little text is found, it's likely scanned
            return len(text_content.strip()) < 100

        except Exception as e:
            logger.warning(f"Error checking PDF type: {str(e)}")
            return False
    
    def _process_regular_pdf(self, file_path: str) -> Dict[str, Any]:
        """
        Process regular PDF using Tabula and Camelot
        """
        transactions = []
        bank_name = None
        extraction_method = 'table_extraction'

        try:
            # Try Tabula first (better for simple tables)
            logger.info("Attempting extraction with Tabula")
            tabula_result = self._extract_with_tabula(file_path)

            if tabula_result['transactions']:
                transactions = tabula_result['transactions']
                bank_name = tabula_result['bank_name']
                extraction_method = 'tabula'
                logger.info(f"Tabula extracted {len(transactions)} transactions")
            else:
                # Try Camelot if Tabula fails
                logger.info("Tabula failed, attempting extraction with Camelot")
                camelot_result = self._extract_with_camelot(file_path)
                transactions = camelot_result['transactions']
                bank_name = camelot_result['bank_name']
                extraction_method = 'camelot'
                logger.info(f"Camelot extracted {len(transactions)} transactions")

        except Exception as e:
            logger.error(f"Error in regular PDF processing: {str(e)}")
            logger.error(f"Processing traceback: {traceback.format_exc()}")
            raise Exception(f"Failed to extract tables from PDF: {str(e)}")

        if not transactions:
            # Provide more specific error message
            error_msg = "No transaction tables found in the PDF. This could be because:\n"
            error_msg += "1. The PDF doesn't contain tabular data\n"
            error_msg += "2. The PDF is scanned/image-based (try OCR processing)\n"
            error_msg += "3. The table format is not recognized\n"
            error_msg += "4. The PDF is password protected or corrupted"
            raise Exception(error_msg)

        return {
            'transactions': transactions,
            'bank_name': bank_name,
            'extraction_method': extraction_method,
            'total_transactions': len(transactions)
        }
    
    def _extract_with_tabula(self, file_path: str) -> Dict[str, Any]:
        """
        Extract tables using Tabula-py
        """
        try:
            logger.info("Starting Tabula extraction...")

            # Read all tables from PDF
            tables = tabula.read_pdf(
                file_path,
                pages='all',
                multiple_tables=True,
                pandas_options={'header': None}
            )

            logger.info(f"Tabula found {len(tables)} tables")

            transactions = []
            bank_name = self._detect_bank_from_pdf(file_path)
            logger.info(f"Detected bank: {bank_name}")

            for i, table in enumerate(tables):
                if isinstance(table, pd.DataFrame) and not table.empty:
                    logger.info(f"Processing table {i+1} with shape {table.shape}")
                    logger.debug(f"Table {i+1} preview:\n{table.head()}")

                    # Process each table to extract transactions
                    table_transactions = self._process_dataframe_to_transactions(table)
                    logger.info(f"Extracted {len(table_transactions)} transactions from table {i+1}")
                    transactions.extend(table_transactions)
                else:
                    logger.warning(f"Table {i+1} is empty or not a DataFrame")

            logger.info(f"Total transactions extracted: {len(transactions)}")

            return {
                'transactions': transactions,
                'bank_name': bank_name
            }

        except Exception as e:
            logger.error(f"Tabula extraction failed: {str(e)}")
            logger.error(f"Tabula traceback: {traceback.format_exc()}")
            return {'transactions': [], 'bank_name': None}
    
    def _extract_with_camelot(self, file_path: str) -> Dict[str, Any]:
        """
        Extract tables using Camelot-py
        """
        if not CAMELOT_AVAILABLE:
            logger.warning("Camelot not available, skipping camelot extraction")
            return {'transactions': [], 'bank_name': None}

        try:
            # Use Camelot to extract tables
            tables = camelot.read_pdf(file_path, pages='all', flavor='lattice')

            # If lattice fails, try stream
            if not tables:
                tables = camelot.read_pdf(file_path, pages='all', flavor='stream')

            transactions = []
            bank_name = self._detect_bank_from_pdf(file_path)

            for table in tables:
                df = table.df
                if not df.empty:
                    table_transactions = self._process_dataframe_to_transactions(df)
                    transactions.extend(table_transactions)

            return {
                'transactions': transactions,
                'bank_name': bank_name
            }

        except Exception as e:
            logger.error(f"Camelot extraction failed: {str(e)}")
            return {'transactions': [], 'bank_name': None}
    
    def _process_scanned_pdf(self, file_path: str) -> Dict[str, Any]:
        """
        Process scanned PDF using OCR - Enhanced for multi-page extraction
        """
        if not OCR_AVAILABLE or not PYMUPDF_AVAILABLE:
            raise Exception("OCR processing not available - missing pytesseract, PIL, or PyMuPDF libraries")

        try:
            logger.info("Processing scanned PDF with OCR")

            # Convert PDF pages to images
            doc = fitz.open(file_path)
            all_transactions = []

            logger.info(f"Processing {doc.page_count} pages with OCR")

            for page_num in range(doc.page_count):
                logger.info(f"Processing page {page_num + 1}/{doc.page_count}")
                page = doc[page_num]

                # Convert page to image with high resolution
                mat = fitz.Matrix(3.0, 3.0)  # Increased resolution for better OCR
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")

                # Convert to PIL Image
                img = Image.open(io.BytesIO(img_data))

                # Preprocess image for better OCR
                processed_img = self._preprocess_image_for_ocr(img)

                # Extract text using OCR with enhanced settings
                ocr_text = pytesseract.image_to_string(
                    processed_img,
                    config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/-: ()'
                )

                logger.debug(f"OCR text from page {page_num + 1}:\n{ocr_text[:500]}...")

                # Process OCR text to extract transactions
                page_transactions = self._extract_hdfc_transactions_from_ocr_text(ocr_text, page_num + 1)

                if page_transactions:
                    all_transactions.extend(page_transactions)
                    logger.info(f"Extracted {len(page_transactions)} transactions from page {page_num + 1}")
                else:
                    logger.warning(f"No transactions found on page {page_num + 1}")

            doc.close()

            # Remove duplicates and sort
            unique_transactions = self._deduplicate_transactions(all_transactions)
            unique_transactions.sort(key=lambda x: self._parse_date_for_sorting(x.get('date', '')))

            logger.info(f"OCR processing complete: {len(unique_transactions)} unique transactions from {doc.page_count} pages")

            return {
                'transactions': unique_transactions,
                'bank_name': 'HDFC',
                'extraction_method': 'ocr',
                'total_transactions': len(unique_transactions)
            }

        except Exception as e:
            logger.error(f"OCR processing failed: {str(e)}")
            raise Exception(f"Failed to process scanned PDF: {str(e)}")
    
    def _preprocess_image_for_ocr(self, img: Image.Image) -> Image.Image:
        """
        Preprocess image to improve OCR accuracy
        """
        if not CV2_AVAILABLE:
            # If OpenCV is not available, return original image
            logger.warning("OpenCV not available, skipping image preprocessing")
            return img

        try:
            # Convert PIL to OpenCV format
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

            # Convert to grayscale
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray)

            # Apply threshold to get binary image
            _, thresh = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Convert back to PIL
            processed_img = Image.fromarray(thresh)

            return processed_img

        except Exception as e:
            logger.warning(f"Image preprocessing failed: {str(e)}")
            return img

    def _process_dataframe_to_transactions(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Convert pandas DataFrame to transaction list
        """
        transactions = []

        try:
            # Clean the dataframe
            df = df.dropna(how='all').reset_index(drop=True)
            logger.debug(f"DataFrame shape after cleaning: {df.shape}")
            logger.debug(f"DataFrame columns: {list(df.columns)}")
            logger.debug(f"First few rows:\n{df.head()}")

            # If DataFrame is too small, skip it
            if df.shape[0] < 2 or df.shape[1] < 3:
                logger.warning(f"DataFrame too small to contain transactions: {df.shape}")
                return []

            # Try to identify columns based on content patterns
            date_col = None
            desc_col = None
            debit_col = None
            credit_col = None
            balance_col = None

            # Analyze each column
            for col_idx, col in enumerate(df.columns):
                col_data = df[col].astype(str).str.strip()
                non_empty_data = col_data[col_data != 'nan'].head(10)  # Sample first 10 non-empty values

                logger.debug(f"Column {col_idx} sample data: {list(non_empty_data)}")

                # Check for date column first
                if self._is_date_column(col_data):
                    date_col = col_idx
                    logger.debug(f"Identified date column: {col_idx}")

                # Check for description column by header names
                elif any(header in str(non_empty_data.iloc[0]).lower() if len(non_empty_data) > 0 else False
                        for header in ['description', 'narration', 'particulars', 'details', 'transaction']):
                    desc_col = col_idx
                    logger.debug(f"Identified description column by header: {col_idx}")

                # Check for amount columns by header names first
                elif any(header in str(non_empty_data.iloc[0]).lower() if len(non_empty_data) > 0 else False
                        for header in ['debit', 'withdrawal', 'dr']):
                    debit_col = col_idx
                    logger.debug(f"Identified debit column by header: {col_idx}")

                elif any(header in str(non_empty_data.iloc[0]).lower() if len(non_empty_data) > 0 else False
                        for header in ['credit', 'deposit', 'cr']):
                    credit_col = col_idx
                    logger.debug(f"Identified credit column by header: {col_idx}")

                elif any(header in str(non_empty_data.iloc[0]).lower() if len(non_empty_data) > 0 else False
                        for header in ['balance', 'bal']):
                    balance_col = col_idx
                    logger.debug(f"Identified balance column by header: {col_idx}")

                # Check for amount columns by content
                elif self._is_amount_column(col_data):
                    if debit_col is None:
                        debit_col = col_idx
                        logger.debug(f"Identified debit column by content: {col_idx}")
                    elif credit_col is None:
                        credit_col = col_idx
                        logger.debug(f"Identified credit column by content: {col_idx}")
                    elif balance_col is None:
                        balance_col = col_idx
                        logger.debug(f"Identified balance column by content: {col_idx}")

                # Description column (usually the longest text column)
                elif desc_col is None and self._is_description_column(col_data):
                    desc_col = col_idx
                    logger.debug(f"Identified description column by content: {col_idx}")

            logger.info(f"Column mapping - Date: {date_col}, Desc: {desc_col}, Debit: {debit_col}, Credit: {credit_col}, Balance: {balance_col}")

            # If we couldn't identify key columns, try alternative approach
            if date_col is None:
                logger.warning("No date column found, trying alternative detection")
                date_col = self._find_date_column_alternative(df)

            if date_col is None:
                logger.warning("Still no date column found, skipping this table")
                return []

            # Process rows to create transactions
            valid_transactions = 0
            for idx, row in df.iterrows():
                try:
                    transaction = self._create_transaction_from_row(
                        row, date_col, desc_col, debit_col, credit_col, balance_col
                    )
                    if transaction:
                        transactions.append(transaction)
                        valid_transactions += 1
                except Exception as e:
                    logger.debug(f"Error processing row {idx}: {str(e)}")
                    continue

            logger.info(f"Successfully processed {valid_transactions} transactions from {len(df)} rows")
            return transactions

        except Exception as e:
            logger.error(f"Error processing DataFrame: {str(e)}")
            logger.error(f"DataFrame processing traceback: {traceback.format_exc()}")
            return []

    def _is_date_column(self, col_data: pd.Series) -> bool:
        """Check if column contains dates"""
        date_count = 0
        total_non_empty = 0

        for value in col_data:
            if pd.notna(value) and str(value).strip() and str(value) != 'nan':
                total_non_empty += 1
                if self._parse_date(str(value)):
                    date_count += 1

        # Lower threshold for date detection and require at least 2 valid dates
        return total_non_empty >= 2 and (date_count / total_non_empty) > 0.3

    def _is_amount_column(self, col_data: pd.Series) -> bool:
        """Check if column contains monetary amounts"""
        amount_count = 0
        total_non_empty = 0

        for value in col_data:
            if pd.notna(value) and str(value).strip() and str(value) != 'nan':
                total_non_empty += 1
                if self._parse_amount(str(value)) is not None:
                    amount_count += 1

        # Lower threshold and require at least 2 valid amounts
        return total_non_empty >= 2 and (amount_count / total_non_empty) > 0.3

    def _is_description_column(self, col_data: pd.Series) -> bool:
        """Check if column contains transaction descriptions"""
        # Description columns typically have longer text
        valid_data = col_data[col_data.notna() & (col_data != 'nan')]
        if len(valid_data) == 0:
            return False
        avg_length = valid_data.str.len().mean()
        return avg_length > 8  # Lower threshold for description detection

    def _find_date_column_alternative(self, df: pd.DataFrame) -> Optional[int]:
        """Alternative method to find date column by looking for date patterns"""
        for col_idx, col in enumerate(df.columns):
            col_data = df[col].astype(str).str.strip()

            # Look for common date patterns in the first few rows
            date_patterns = [
                r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',  # DD/MM/YYYY or DD-MM-YYYY
                r'\d{2,4}[/-]\d{1,2}[/-]\d{1,2}',  # YYYY/MM/DD or YYYY-MM-DD
                r'\d{1,2}\s+\w{3}\s+\d{2,4}',      # DD MMM YYYY
                r'\w{3}\s+\d{1,2},?\s+\d{2,4}'     # MMM DD, YYYY
            ]

            date_like_count = 0
            sample_size = min(10, len(col_data))

            for value in col_data.head(sample_size):
                if pd.notna(value) and str(value) != 'nan':
                    for pattern in date_patterns:
                        if re.search(pattern, str(value)):
                            date_like_count += 1
                            break

            if date_like_count >= 2:  # At least 2 date-like values
                logger.info(f"Alternative date detection found column {col_idx} with {date_like_count} date-like values")
                return col_idx

        return None

    def _create_transaction_from_row(self, row, date_col, desc_col, debit_col, credit_col, balance_col) -> Optional[Dict[str, Any]]:
        """Create transaction dictionary from DataFrame row"""
        try:
            # Extract date
            if date_col is not None:
                date_str = str(row.iloc[date_col]).strip()
                parsed_date = self._parse_date(date_str)
                if not parsed_date:
                    return None
            else:
                return None

            # Skip header rows
            if 'date' in date_str.lower() or 'dt' in date_str.lower():
                return None

            # Extract description
            description = ""
            if desc_col is not None:
                description = str(row.iloc[desc_col]).strip()
                # Skip header rows
                if description.lower() in ['description', 'narration', 'particulars', 'details']:
                    return None

            # Extract amounts
            debit_amount = None
            credit_amount = None
            balance_amount = None

            if debit_col is not None:
                debit_amount = self._parse_amount(str(row.iloc[debit_col]))

            if credit_col is not None:
                credit_amount = self._parse_amount(str(row.iloc[credit_col]))

            if balance_col is not None:
                balance_amount = self._parse_amount(str(row.iloc[balance_col]))

            # Skip rows with no meaningful data
            if not description and debit_amount is None and credit_amount is None:
                return None

            # Create transaction
            transaction = {
                'id': f"txn_{datetime.now().timestamp()}_{hash(str(row))}",
                'date': parsed_date,
                'description': description,
                'debit': debit_amount,
                'credit': credit_amount,
                'balance': balance_amount
            }

            return transaction

        except Exception as e:
            logger.warning(f"Error creating transaction from row: {str(e)}")
            return None

    def _parse_date(self, date_str: str) -> Optional[str]:
        """Parse date string to standard format"""
        if not date_str or date_str.lower() in ['nan', 'none', '', 'null']:
            return None

        # Clean the date string
        date_str = str(date_str).strip()

        # Remove common prefixes/suffixes
        date_str = re.sub(r'^(date|dt|txn|transaction)[\s:]*', '', date_str, flags=re.IGNORECASE)
        date_str = re.sub(r'[^\d/\-.\s\w]', '', date_str).strip()

        # If it's just numbers, skip it
        if date_str.isdigit():
            return None

        # Extended date formats for Indian bank statements
        extended_formats = [
            '%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y',
            '%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d',
            '%d/%m/%y', '%d-%m-%y', '%d.%m.%y',
            '%d %b %Y', '%d %B %Y', '%b %d, %Y',
            '%d-%b-%Y', '%d-%B-%Y',
            '%d %b %y', '%d %B %y',
            '%d-%b-%y', '%d-%B-%y',
            '%d%m%Y', '%d%m%y',  # DDMMYYYY, DDMMYY
            '%Y%m%d'  # YYYYMMDD
        ]

        for fmt in extended_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                # Ensure year is reasonable (between 1900 and 2100)
                if 1900 <= parsed_date.year <= 2100:
                    return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue

        return None

    def _parse_amount(self, amount_str: str) -> Optional[float]:
        """Parse amount string to float"""
        if not amount_str or amount_str.lower() in ['nan', 'none', '', 'null', '-']:
            return None

        try:
            # Convert to string and clean
            amount_str = str(amount_str).strip()

            # Remove common prefixes/suffixes
            amount_str = re.sub(r'^(rs\.?|inr|₹)\s*', '', amount_str, flags=re.IGNORECASE)
            amount_str = re.sub(r'\s*(cr|dr|debit|credit)$', '', amount_str, flags=re.IGNORECASE)

            # Handle parentheses (negative amounts)
            is_negative = False
            if amount_str.startswith('(') and amount_str.endswith(')'):
                is_negative = True
                amount_str = amount_str[1:-1]

            # Remove currency symbols and keep only digits, commas, dots, and minus
            cleaned = re.sub(r'[^\d.,\-]', '', amount_str)

            if not cleaned or cleaned in ['-', '.', ',']:
                return None

            # Handle Indian number format (lakhs/crores with commas)
            # Remove commas for parsing
            cleaned = cleaned.replace(',', '')

            # Handle multiple dots (keep only the last one as decimal point)
            if cleaned.count('.') > 1:
                parts = cleaned.split('.')
                cleaned = ''.join(parts[:-1]) + '.' + parts[-1]

            # Parse the number
            amount = float(cleaned)

            # Apply negative sign if needed
            if is_negative:
                amount = -amount

            # Sanity check - amounts should be reasonable
            if abs(amount) > 1e12:  # More than 1 trillion
                return None

            return amount

        except (ValueError, TypeError):
            return None

    def _extract_transactions_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract transactions from OCR text"""
        transactions = []

        try:
            lines = text.split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Try to match transaction patterns
                transaction = self._parse_transaction_line(line)
                if transaction:
                    transactions.append(transaction)

            return transactions

        except Exception as e:
            logger.error(f"Error extracting transactions from text: {str(e)}")
            return []

    def _parse_transaction_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a single line to extract transaction data"""
        try:
            # Common transaction patterns
            patterns = [
                # Date Description Amount Amount Balance
                r'(\d{1,2}[/\-\.]\d{1,2}[/\-\.]\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
                # Date Description Amount Balance
                r'(\d{1,2}[/\-\.]\d{1,2}[/\-\.]\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
                # More flexible pattern
                r'(\d{1,2}[/\-\.]\d{1,2}[/\-\.]\d{2,4})\s+(.+?)(?:\s+([\d,]+\.?\d*))?(?:\s+([\d,]+\.?\d*))?(?:\s+([\d,]+\.?\d*))?'
            ]

            for pattern in patterns:
                match = re.search(pattern, line)
                if match:
                    groups = match.groups()

                    date_str = groups[0]
                    description = groups[1].strip()

                    # Parse date
                    parsed_date = self._parse_date(date_str)
                    if not parsed_date:
                        continue

                    # Parse amounts
                    amounts = [self._parse_amount(g) for g in groups[2:] if g]
                    amounts = [a for a in amounts if a is not None]

                    # Determine debit, credit, balance
                    debit = None
                    credit = None
                    balance = None

                    if len(amounts) >= 3:
                        debit = amounts[0] if amounts[0] != 0 else None
                        credit = amounts[1] if amounts[1] != 0 else None
                        balance = amounts[2]
                    elif len(amounts) == 2:
                        # Could be debit/credit and balance
                        credit = amounts[0] if amounts[0] > 0 else None
                        debit = abs(amounts[0]) if amounts[0] < 0 else None
                        balance = amounts[1]
                    elif len(amounts) == 1:
                        balance = amounts[0]

                    transaction = {
                        'id': f"txn_{datetime.now().timestamp()}_{hash(line)}",
                        'date': parsed_date,
                        'description': description,
                        'debit': debit,
                        'credit': credit,
                        'balance': balance
                    }

                    return transaction

            return None

        except Exception as e:
            logger.warning(f"Error parsing transaction line: {str(e)}")
            return None

    def _detect_bank_from_pdf(self, file_path: str) -> Optional[str]:
        """Detect bank name from PDF content"""
        text_content = ""

        # Try PyMuPDF first
        if PYMUPDF_AVAILABLE:
            try:
                doc = fitz.open(file_path)
                # Check first page for bank identification
                if doc.page_count > 0:
                    page = doc[0]
                    text_content = page.get_text().upper()
                doc.close()
            except Exception as e:
                logger.warning(f"PyMuPDF bank detection failed: {str(e)}")

        # Fallback to pdfplumber if PyMuPDF failed
        if not text_content and PDFPLUMBER_AVAILABLE:
            try:
                with pdfplumber.open(file_path) as pdf:
                    if pdf.pages:
                        page_text = pdf.pages[0].extract_text()
                        if page_text:
                            text_content = page_text.upper()
            except Exception as e:
                logger.warning(f"pdfplumber bank detection failed: {str(e)}")

        # If we have text content, try to detect bank
        if text_content:
            # Bank detection patterns
            bank_patterns = {
                'SBI': ['STATE BANK OF INDIA', 'SBI'],
                'HDFC': ['HDFC BANK', 'HDFC'],
                'ICICI': ['ICICI BANK', 'ICICI'],
                'AXIS': ['AXIS BANK', 'AXIS'],
                'KOTAK': ['KOTAK MAHINDRA', 'KOTAK'],
                'PNB': ['PUNJAB NATIONAL BANK', 'PNB'],
                'BOB': ['BANK OF BARODA', 'BOB'],
                'CANARA': ['CANARA BANK', 'CANARA'],
                'UNION': ['UNION BANK', 'UNION']
            }

            for bank_id, patterns in bank_patterns.items():
                for pattern in patterns:
                    if pattern in text_content:
                        return bank_id

        return 'GENERIC'

    def _extract_text_for_detection(self, file_path: str) -> str:
        """
        Extract text from PDF for bank detection
        """
        text_content = ""

        # Try PyMuPDF first
        if PYMUPDF_AVAILABLE:
            try:
                doc = fitz.open(file_path)
                # Extract text from first 2 pages for detection
                pages_to_check = min(2, doc.page_count)

                for page_num in range(pages_to_check):
                    page = doc[page_num]
                    text_content += page.get_text()

                doc.close()
                logger.debug(f"Extracted {len(text_content)} characters for detection")
                return text_content

            except Exception as e:
                logger.warning(f"PyMuPDF text extraction failed: {str(e)}")

        # Fallback to pdfplumber
        if PDFPLUMBER_AVAILABLE:
            try:
                with pdfplumber.open(file_path) as pdf:
                    pages_to_check = min(2, len(pdf.pages))
                    for i in range(pages_to_check):
                        page_text = pdf.pages[i].extract_text()
                        if page_text:
                            text_content += page_text

                logger.debug(f"Extracted {len(text_content)} characters using pdfplumber")
                return text_content

            except Exception as e:
                logger.warning(f"pdfplumber text extraction failed: {str(e)}")

        return text_content

    def _process_hdfc_pdf(self, file_path: str) -> Dict[str, Any]:
        """
        Process HDFC Bank PDF using specialized HDFC processor - FIXED: Single strategy to prevent duplicates
        """
        transactions = []

        try:
            logger.info("Processing HDFC Bank PDF with specialized processor")

            # FIXED: Use single most effective strategy to prevent massive duplication
            all_tables = []

            # Primary Strategy: HDFC-optimized lattice extraction with auto-detection (FIXED for accuracy)
            try:
                tables_primary = tabula.read_pdf(
                    file_path,
                    pages='all',
                    multiple_tables=True,
                    lattice=True,
                    # REMOVED fixed columns to prevent data misalignment
                    pandas_options={'header': None, 'dtype': str},
                    silent=True
                )
                all_tables.extend(tables_primary)
                logger.info(f"Primary strategy: Extracted {len(tables_primary)} tables with auto-detected lattice")
            except Exception as e:
                logger.warning(f"Primary strategy failed: {str(e)}")

            # Fallback Strategy 1: Standard lattice without column specification
            if not all_tables:
                try:
                    tables_fallback1 = tabula.read_pdf(
                        file_path,
                        pages='all',
                        multiple_tables=True,
                        lattice=True,
                        pandas_options={'header': None, 'dtype': str},
                        silent=True
                    )
                    all_tables.extend(tables_fallback1)
                    logger.info(f"Fallback 1: Extracted {len(tables_fallback1)} tables with standard lattice")
                except Exception as e:
                    logger.warning(f"Fallback 1 failed: {str(e)}")

            # Fallback Strategy 2: Stream extraction for text-based tables
            if not all_tables:
                try:
                    tables_fallback2 = tabula.read_pdf(
                        file_path,
                        pages='all',
                        multiple_tables=True,
                        stream=True,
                        pandas_options={'header': None, 'dtype': str},
                        silent=True
                    )
                    all_tables.extend(tables_fallback2)
                    logger.info(f"Fallback 2: Extracted {len(tables_fallback2)} tables with stream extraction")
                except Exception as e:
                    logger.warning(f"Fallback 2 failed: {str(e)}")

            # FIXED: Process tables with improved deduplication to prevent transaction multiplication
            logger.info(f"Processing {len(all_tables)} extracted tables")

            # Remove duplicate tables first to prevent processing same data multiple times
            unique_tables = self._deduplicate_tables(all_tables)
            logger.info(f"After table deduplication: {len(unique_tables)} unique tables")

            # Process each unique table
            for i, table in enumerate(unique_tables):
                if isinstance(table, pd.DataFrame) and not table.empty:
                    logger.info(f"Processing HDFC table {i+1}/{len(unique_tables)} with shape {table.shape}")
                    logger.debug(f"Table {i+1} preview:\n{table.head()}")

                    # Use HDFC processor to extract transactions
                    table_transactions = self.hdfc_processor.process_hdfc_dataframe(table)

                    if table_transactions:
                        transactions.extend(table_transactions)
                        logger.info(f"Table {i+1}: {len(table_transactions)} transactions extracted")
                    else:
                        logger.warning(f"No transactions extracted from HDFC table {i+1}")
                        # Log table details for debugging
                        logger.debug(f"Table {i+1} columns: {list(table.columns)}")
                        logger.debug(f"Table {i+1} sample data:\n{table.head(3)}")
                else:
                    logger.debug(f"Skipping table {i+1}: empty or not DataFrame")

            logger.info(f"Raw transactions extracted: {len(transactions)}")

            # If still no transactions, try Camelot as fallback
            if not transactions:
                logger.info("No transactions found with Tabula strategies, trying Camelot for HDFC")
                camelot_transactions = self._extract_hdfc_with_camelot(file_path)
                transactions.extend(camelot_transactions)

            # If still no transactions, try generic extraction as last resort
            if not transactions:
                logger.info("No transactions found with specialized methods, trying generic extraction")
                generic_transactions = self._extract_hdfc_generic_fallback(file_path)
                transactions.extend(generic_transactions)

            if not transactions:
                raise Exception("No HDFC Bank transactions found in the PDF. Please ensure this is a valid HDFC Bank statement with transaction data.")

            # FIXED: Enhanced deduplication and sorting
            pre_dedup_count = len(transactions)
            logger.info(f"Before deduplication: {pre_dedup_count} transactions")

            # Remove duplicates with enhanced logic
            transactions = self._deduplicate_transactions_enhanced(transactions)

            # Sort transactions by date
            transactions.sort(key=lambda x: self._parse_date_for_sorting(x.get('date', '')))

            logger.info(f"Final HDFC extraction result: {len(transactions)} unique transactions")

            # Log deduplication impact
            if pre_dedup_count > len(transactions):
                duplicates_removed = pre_dedup_count - len(transactions)
                logger.info(f"Deduplication removed {duplicates_removed} duplicate transactions")
            else:
                logger.info("No duplicate transactions found")

            return {
                'transactions': transactions,
                'bank_name': 'HDFC',
                'extraction_method': 'hdfc_specialized',
                'total_transactions': len(transactions)
            }

        except Exception as e:
            logger.error(f"Error processing HDFC PDF: {str(e)}")
            logger.error(f"HDFC processing traceback: {traceback.format_exc()}")
            raise Exception(f"Failed to process HDFC Bank statement: {str(e)}")

    def _deduplicate_tables(self, tables: List[pd.DataFrame]) -> List[pd.DataFrame]:
        """
        FIXED: Conservative table deduplication to prevent losing valid transactions
        """
        if not tables:
            return []

        unique_tables = []
        logger.info(f"FIXED: Conservative deduplication of {len(tables)} tables")

        for i, table in enumerate(tables):
            if not isinstance(table, pd.DataFrame) or table.empty:
                logger.debug(f"Skipping table {i+1}: empty or not DataFrame")
                continue

            is_duplicate = False

            # FIXED: Only remove tables that are EXACTLY identical
            for j, existing_table in enumerate(unique_tables):
                if self._are_tables_exactly_duplicate(table, existing_table):
                    is_duplicate = True
                    logger.debug(f"Table {i+1} is EXACT duplicate of table {j+1}")
                    break

            if not is_duplicate:
                unique_tables.append(table)
                logger.debug(f"Table {i+1} added as unique (shape: {table.shape})")

        logger.info(f"FIXED: Conservative table deduplication: {len(tables)} -> {len(unique_tables)} unique tables")
        return unique_tables

    def _are_tables_exactly_duplicate(self, table1: pd.DataFrame, table2: pd.DataFrame) -> bool:
        """
        FIXED: Check if two tables are EXACTLY identical to prevent losing valid data
        """
        try:
            # Must have same shape
            if table1.shape != table2.shape:
                return False

            # FIXED: Only consider exact duplicates - same content in every cell
            return table1.equals(table2)

        except Exception as e:
            logger.debug(f"Error comparing tables for exact duplication: {str(e)}")
            return False

    def _are_tables_duplicate(self, table1: pd.DataFrame, table2: pd.DataFrame) -> bool:
        """
        Enhanced check if two tables are duplicates
        """
        try:
            # Quick shape check
            if table1.shape != table2.shape:
                return False

            # If both tables are small, compare content directly
            if table1.shape[0] <= 10:
                return table1.equals(table2)

            # For larger tables, compare structure and sample content
            # Compare first and last few rows
            sample_size = min(3, table1.shape[0])

            head_match = table1.head(sample_size).equals(table2.head(sample_size))
            tail_match = table1.tail(sample_size).equals(table2.tail(sample_size))

            return head_match and tail_match

        except Exception as e:
            logger.debug(f"Error comparing tables: {str(e)}")
            return False

    def _create_transaction_hash(self, transaction: Dict[str, Any]) -> str:
        """
        Create a unique hash for a transaction to identify ONLY exact duplicates
        FIXED: Less aggressive to prevent removing valid transactions
        """
        try:
            # Use only core fields to avoid over-aggressive deduplication
            date = transaction.get('date', '')
            narration = (transaction.get('narration', '') or '').strip()
            withdrawal = transaction.get('withdrawal_amt', 0) or 0
            deposit = transaction.get('deposit_amt', 0) or 0

            # Only consider exact duplicates - same date, narration, and amounts
            # Removed balance and other fields to be less aggressive
            hash_string = f"{date}|{narration}|{withdrawal}|{deposit}"

            return str(hash(hash_string))
        except:
            # Fallback to string representation
            return str(hash(str(transaction)))

    def _get_table_hash(self, df: pd.DataFrame) -> str:
        """
        Generate a hash for table content to identify duplicates
        """
        try:
            # Create a string representation of the table structure and first few rows
            content = f"{df.shape}_{df.head(3).to_string()}"
            return str(hash(content))
        except:
            return str(hash(str(df.shape)))

    def _extract_hdfc_with_camelot(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Extract HDFC transactions using Camelot as fallback
        """
        transactions = []

        if not CAMELOT_AVAILABLE:
            logger.warning("Camelot not available for fallback extraction")
            return transactions

        try:
            # Try lattice first
            camelot_tables = camelot.read_pdf(file_path, pages='all', flavor='lattice')
            if not camelot_tables:
                # Try stream if lattice fails
                camelot_tables = camelot.read_pdf(file_path, pages='all', flavor='stream')

            logger.info(f"Camelot extracted {len(camelot_tables)} tables")

            for i, table in enumerate(camelot_tables):
                df = table.df
                if not df.empty:
                    logger.info(f"Processing Camelot table {i+1} with shape {df.shape}")
                    table_transactions = self.hdfc_processor.process_hdfc_dataframe(df)
                    if table_transactions:
                        transactions.extend(table_transactions)
                        logger.info(f"Extracted {len(table_transactions)} transactions from Camelot table {i+1}")

        except Exception as e:
            logger.warning(f"Camelot fallback extraction failed: {str(e)}")

        return transactions

    def _extract_hdfc_generic_fallback(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Generic fallback extraction for HDFC when specialized methods fail
        """
        transactions = []

        try:
            logger.info("Attempting generic fallback extraction for HDFC")

            # Try to extract with more relaxed Tabula settings
            tables = tabula.read_pdf(
                file_path,
                pages='all',
                multiple_tables=True,
                guess=True,  # Let Tabula guess the table structure
                pandas_options={'header': None}
            )

            for i, table in enumerate(tables):
                if isinstance(table, pd.DataFrame) and not table.empty:
                    logger.info(f"Processing generic table {i+1} with shape {table.shape}")

                    # Try generic transaction extraction
                    table_transactions = self._process_dataframe_to_transactions(table)
                    if table_transactions:
                        # Transform generic transactions to HDFC format
                        hdfc_transactions = self._transform_generic_to_hdfc_format(table_transactions)
                        transactions.extend(hdfc_transactions)
                        logger.info(f"Extracted {len(hdfc_transactions)} transactions from generic table {i+1}")

        except Exception as e:
            logger.warning(f"Generic fallback extraction failed: {str(e)}")

        return transactions

    def _transform_generic_to_hdfc_format(self, generic_transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Transform generic transaction format to HDFC-specific format
        """
        hdfc_transactions = []

        for txn in generic_transactions:
            try:
                # Transform field names from generic to HDFC format
                hdfc_txn = {
                    'id': txn.get('id', ''),
                    'date': txn.get('date', ''),
                    'narration': txn.get('description', ''),  # description -> narration
                    'chq_ref_no': '',  # Not available in generic extraction
                    'value_date': txn.get('date', ''),  # Use same as transaction date
                    'withdrawal_amt': txn.get('debit'),  # debit -> withdrawal_amt
                    'deposit_amt': txn.get('credit'),  # credit -> deposit_amt
                    'closing_balance': txn.get('balance')  # balance -> closing_balance
                }

                hdfc_transactions.append(hdfc_txn)

            except Exception as e:
                logger.warning(f"Error transforming transaction to HDFC format: {str(e)}")
                continue

        logger.info(f"Transformed {len(generic_transactions)} generic transactions to {len(hdfc_transactions)} HDFC format transactions")
        return hdfc_transactions

    def _deduplicate_transactions(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        FIXED: Very conservative transaction deduplication to prevent losing valid transactions
        """
        seen_transactions = set()
        unique_transactions = []

        for txn in transactions:
            # Create a unique key for the transaction - only for EXACT duplicates
            key_parts = [
                txn.get('date', ''),
                (txn.get('narration', txn.get('description', '')) or '').strip(),
                str(txn.get('withdrawal_amt', txn.get('debit', '')) or ''),
                str(txn.get('deposit_amt', txn.get('credit', '')) or '')
                # Removed balance from key as it can vary slightly due to rounding/formatting
            ]

            txn_key = '|'.join(key_parts)

            # Only remove if it's an EXACT duplicate (same date, narration, and amounts)
            if txn_key not in seen_transactions or not all(key_parts[:4]):  # Allow if any key part is empty
                seen_transactions.add(txn_key)
                unique_transactions.append(txn)
            else:
                logger.debug(f"Skipping exact duplicate transaction: {txn.get('date')} - {txn.get('narration', txn.get('description', ''))[:50]}")

        logger.info(f"Deduplication: {len(transactions)} -> {len(unique_transactions)} transactions")
        return unique_transactions

    def _deduplicate_transactions_enhanced(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enhanced deduplication to handle massive duplicates from multiple extraction strategies
        """
        if not transactions:
            return []

        logger.info(f"Starting enhanced deduplication with {len(transactions)} transactions")

        # Step 1: Remove exact duplicates (including balance)
        seen_exact = set()
        after_exact = []

        for txn in transactions:
            # Create exact match key including balance
            exact_key = '|'.join([
                str(txn.get('date', '')),
                str(txn.get('narration', txn.get('description', '')) or '').strip(),
                str(txn.get('chq_ref_no', txn.get('refNo', '')) or ''),
                str(txn.get('value_date', txn.get('valueDate', '')) or ''),
                str(txn.get('withdrawal_amt', txn.get('debit', '')) or ''),
                str(txn.get('deposit_amt', txn.get('credit', '')) or ''),
                str(txn.get('closing_balance', txn.get('balance', '')) or '')
            ])

            if exact_key not in seen_exact:
                seen_exact.add(exact_key)
                after_exact.append(txn)

        logger.info(f"After exact deduplication: {len(after_exact)} transactions")

        # Step 2: Remove near-duplicates (same core data, different balance)
        seen_core = set()
        after_core = []

        for txn in after_exact:
            # Create core match key (without balance)
            core_key = '|'.join([
                str(txn.get('date', '')),
                str(txn.get('narration', txn.get('description', '')) or '').strip(),
                str(txn.get('withdrawal_amt', txn.get('debit', '')) or ''),
                str(txn.get('deposit_amt', txn.get('credit', '')) or '')
            ])

            if core_key not in seen_core:
                seen_core.add(core_key)
                after_core.append(txn)
            else:
                logger.debug(f"Removing near-duplicate: {txn.get('date')} - {str(txn.get('narration', txn.get('description', '')))[:30]}")

        logger.info(f"After core deduplication: {len(after_core)} transactions")

        # Step 3: Filter out header rows and invalid transactions
        valid_transactions = []
        for txn in after_core:
            if self._is_valid_transaction_row(txn):
                valid_transactions.append(txn)
            else:
                logger.debug(f"Filtering out invalid/header row: {txn}")

        logger.info(f"After validation filtering: {len(valid_transactions)} transactions")
        return valid_transactions

    def _is_valid_transaction_row(self, txn: Dict[str, Any]) -> bool:
        """
        Check if a transaction row is valid (not a header or empty row)
        """
        # Check for common header patterns
        narration = str(txn.get('narration', txn.get('description', '')) or '').strip().lower()
        date = str(txn.get('date', '')).strip()

        # Skip header rows - be more specific to avoid false positives
        header_patterns = [
            'date narration', 'date description', 'date particulars',
            'opening balance', 'closing balance', 'total balance',
            'carried forward', 'brought forward', 'statement period',
            'account number', 'account summary'
        ]

        # Only reject if narration contains complete header phrases, not individual words
        if any(pattern in narration for pattern in header_patterns):
            return False

        # Must have a valid date (relaxed requirement)
        if not date or len(date) < 4:
            return False

        # Must have either withdrawal or deposit amount (check for valid numeric values)
        withdrawal = txn.get('withdrawal_amt', txn.get('debit'))
        deposit = txn.get('deposit_amt', txn.get('credit'))

        # Convert to float and check if valid
        try:
            withdrawal_val = float(withdrawal) if withdrawal and str(withdrawal).strip() not in ['', 'None', 'nan'] else 0
            deposit_val = float(deposit) if deposit and str(deposit).strip() not in ['', 'None', 'nan'] else 0
        except (ValueError, TypeError):
            withdrawal_val = 0
            deposit_val = 0

        # Must have at least one non-zero amount
        if withdrawal_val == 0 and deposit_val == 0:
            return False

        # Narration can be empty for legitimate transactions (removed strict requirement)
        # Many bank transactions have empty descriptions but are still valid

        return True

    def _parse_date_for_sorting(self, date_str: str) -> str:
        """
        Parse date string for sorting purposes
        """
        if not date_str:
            return '1900-01-01'

        try:
            # Try different date formats
            for fmt in ['%d/%m/%y', '%d/%m/%Y', '%d-%m-%y', '%d-%m-%Y', '%d.%m.%y', '%d.%m.%Y']:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue

            # If no format matches, return original
            return date_str

        except Exception as e:
            logger.warning(f"Error parsing date for sorting: {date_str} - {str(e)}")
            return '1900-01-01'

    def _extract_hdfc_transactions_from_ocr_text(self, text: str, page_num: int) -> List[Dict[str, Any]]:
        """
        Extract HDFC transactions from OCR text with enhanced pattern matching
        """
        transactions = []

        try:
            lines = text.split('\n')
            logger.debug(f"Processing {len(lines)} lines from OCR text on page {page_num}")

            # Enhanced HDFC transaction patterns
            hdfc_patterns = [
                # Pattern 1: DD/MM/YY Description Amount Amount Amount
                r'(\d{2}[/\-\.]\d{2}[/\-\.]\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
                # Pattern 2: DD/MM/YY Description Ref Amount Amount
                r'(\d{2}[/\-\.]\d{2}[/\-\.]\d{2,4})\s+(.+?)\s+(\w+\d+)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
                # Pattern 3: DD/MM/YY Description Amount Balance
                r'(\d{2}[/\-\.]\d{2}[/\-\.]\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
                # Pattern 4: More flexible pattern
                r'(\d{2}[/\-\.]\d{2}[/\-\.]\d{2,4})\s+(.+?)(?:\s+([\d,]+\.?\d*))?(?:\s+([\d,]+\.?\d*))?(?:\s+([\d,]+\.?\d*))?'
            ]

            for line_num, line in enumerate(lines):
                line = line.strip()
                if not line or len(line) < 10:
                    continue

                # Skip header lines
                if any(header in line.lower() for header in ['date', 'narration', 'particulars', 'balance', 'withdrawal', 'deposit']):
                    continue

                for pattern in hdfc_patterns:
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        groups = match.groups()

                        # Parse date
                        date_str = groups[0]
                        parsed_date = self._parse_date_for_ocr(date_str)
                        if not parsed_date:
                            continue

                        # Parse description
                        description = groups[1].strip() if len(groups) > 1 else ""
                        if len(description) < 3:  # Skip very short descriptions
                            continue

                        # Parse amounts
                        amounts = []
                        for i in range(2, len(groups)):
                            if groups[i]:
                                amount = self._parse_amount_for_ocr(groups[i])
                                if amount is not None:
                                    amounts.append(amount)

                        if not amounts:  # Skip if no valid amounts found
                            continue

                        # Determine transaction type and amounts
                        withdrawal_amt = None
                        deposit_amt = None
                        balance_amt = None

                        if len(amounts) >= 3:
                            # Likely: withdrawal, deposit, balance
                            withdrawal_amt = amounts[0] if amounts[0] > 0 else None
                            deposit_amt = amounts[1] if amounts[1] > 0 else None
                            balance_amt = amounts[2]
                        elif len(amounts) == 2:
                            # Likely: transaction amount, balance
                            if amounts[0] > 0:
                                # Determine if it's withdrawal or deposit based on context
                                if 'credit' in description.lower() or 'deposit' in description.lower():
                                    deposit_amt = amounts[0]
                                else:
                                    withdrawal_amt = amounts[0]
                            balance_amt = amounts[1]
                        elif len(amounts) == 1:
                            balance_amt = amounts[0]

                        # Create transaction
                        transaction = {
                            'id': f"hdfc_ocr_{page_num}_{line_num}_{hash(line)}",
                            'date': parsed_date,
                            'narration': description,
                            'chq_ref_no': '',
                            'value_date': '',
                            'withdrawal_amt': withdrawal_amt,
                            'deposit_amt': deposit_amt,
                            'closing_balance': balance_amt,
                            'bank': 'HDFC'
                        }

                        # Validate transaction
                        if self._validate_ocr_transaction(transaction):
                            transactions.append(transaction)
                            logger.debug(f"OCR transaction extracted: {parsed_date} - {description[:30]}")

                        break  # Found a match, move to next line

            logger.info(f"Extracted {len(transactions)} transactions from OCR text on page {page_num}")
            return transactions

        except Exception as e:
            logger.error(f"Error extracting transactions from OCR text: {str(e)}")
            return []

    def _parse_date_for_ocr(self, date_str: str) -> Optional[str]:
        """
        Parse date from OCR text with error tolerance
        """
        if not date_str:
            return None

        # Clean OCR artifacts
        date_str = re.sub(r'[^\d/\-\.]', '', date_str)

        date_formats = [
            '%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y',
            '%d/%m/%y', '%d-%m-%y', '%d.%m.%y'
        ]

        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                if 1990 <= parsed_date.year <= datetime.now().year + 1:
                    return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue

        return None

    def _parse_amount_for_ocr(self, amount_str: str) -> Optional[float]:
        """
        Parse amount from OCR text with error tolerance
        """
        if not amount_str:
            return None

        try:
            # Clean OCR artifacts
            cleaned = re.sub(r'[^\d.,\-]', '', amount_str)
            if not cleaned or cleaned in ['-', '.', ',']:
                return None

            # Handle Indian number format
            cleaned = cleaned.replace(',', '')

            amount = float(cleaned)
            return amount if amount >= 0 else None

        except (ValueError, TypeError):
            return None

    def _validate_ocr_transaction(self, transaction: Dict[str, Any]) -> bool:
        """
        Validate OCR-extracted transaction
        """
        try:
            # Must have valid date
            if not transaction.get('date'):
                return False

            # Must have description or amounts
            has_description = bool(transaction.get('narration', '').strip())
            has_amounts = (transaction.get('withdrawal_amt') is not None or
                          transaction.get('deposit_amt') is not None or
                          transaction.get('closing_balance') is not None)

            if not (has_description or has_amounts):
                return False

            # Description should be meaningful (not just numbers or single characters)
            description = transaction.get('narration', '').strip()
            if description and (len(description) < 3 or description.isdigit()):
                return False

            return True

        except Exception:
            return False
